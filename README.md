# 现代化商品展示系统 🛍️

<div align="center">

![React](https://img.shields.io/badge/React-18.2.0-blue?logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.2.2-blue?logo=typescript)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green?logo=spring)
![Java](https://img.shields.io/badge/Java-8+-orange?logo=java)
![Vite](https://img.shields.io/badge/Vite-5.0.0-purple?logo=vite)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.3.5-blue?logo=tailwindcss)

**基于现代化技术栈构建的高性能电商商品展示系统**

[在线演示](https://demo.example.com) • [API文档](http://localhost:8080/swagger-ui.html) • [技术文档](docs/)

</div>

## ✨ 项目特色

- 🚀 **现代化架构** - 前后端分离，微服务友好设计
- 📱 **响应式设计** - 完美适配桌面、平板、移动设备
- ⚡ **极致性能** - 多级缓存、懒加载、代码分割优化
- 🔒 **类型安全** - 全栈TypeScript/Java强类型保障
- 🛠️ **开发友好** - 热更新、自动重启、完善的开发工具链
- 🎨 **现代UI** - 基于Tailwind CSS的精美界面设计

## 🛠️ 技术栈

### 前端技术栈
| 技术 | 版本 | 说明 |
|------|------|------|
| React | 18.2.0 | 现代化前端框架，支持并发特性 |
| TypeScript | 5.2.2 | 类型安全的JavaScript超集 |
| Vite | 5.0.0 | 极速构建工具，支持热更新 |
| Tailwind CSS | 3.3.5 | 实用优先的CSS框架 |
| React Query | 5.8.4 | 强大的数据获取和缓存库 |
| Zustand | 4.4.7 | 轻量级状态管理库 |
| React Router | 6.20.1 | 客户端路由管理 |
| Axios | 1.6.2 | HTTP客户端库 |

### 后端技术栈
| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.7.18 | 企业级Java框架 |
| Spring Data JPA | - | 数据访问层抽象 |
| H2 Database | - | 内存数据库（开发环境） |
| MySQL | 8.0 | 生产环境数据库 |
| Redis | 6.0+ | 分布式缓存 |
| SpringDoc OpenAPI | 1.7.0 | API文档生成 |
| Maven | 3.6+ | 项目构建和依赖管理 |

## 🚀 快速开始

### 环境要求
```bash
node --version    # >= 16.0.0
java -version     # >= 8 (推荐 11+)
mvn --version     # >= 3.6.0
git --version     # >= 2.0.0
```

### 一键启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd aug_pop

# 2. 启动后端服务
cd springboot-backend
mvn spring-boot:run

# 3. 启动前端服务 (新终端窗口)
cd vite-frontend
npm install
npm run dev
```

### 访问应用
- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8080
- 📚 **API文档**: http://localhost:8080/swagger-ui.html
- 💾 **H2控制台**: http://localhost:8080/h2-console

## 📱 功能展示

### 🏠 商品列表页
- 响应式网格布局
- 实时搜索功能
- 分类筛选
- 商品卡片展示
- 购物车功能
- 无限滚动加载

### 📦 商品详情页
- 商品图片轮播
- 详细信息展示
- 规格参数表格
- 用户评价
- 相关推荐
- 加入购物车

### 🛒 购物车功能
- 商品数量管理
- 价格计算
- 本地存储
- 同步到服务器

## 🛠️ 技术栈

### 前端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| React | 18.2+ | 用户界面库 |
| Vite | 5.0+ | 构建工具 |
| TypeScript | 5.0+ | 类型安全 |
| Tailwind CSS | 3.3+ | 原子化CSS |
| React Router | 6.20+ | 客户端路由 |
| React Query | 5.8+ | 服务端状态管理 |
| Zustand | 4.4+ | 客户端状态管理 |
| Axios | 1.6+ | HTTP客户端 |

### 后端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Spring Boot | 3.2+ | Java框架 |
| Spring Security | 6.0+ | 安全框架 |
| Spring Data JPA | 3.2+ | 数据访问 |
| MySQL | 8.0+ | 关系型数据库 |
| Redis | 6.0+ | 缓存数据库 |
| Maven | 3.8+ | 构建工具 |

## 📊 性能优势

| 指标 | 传统SSR | 当前方案 | 提升 |
|------|---------|----------|------|
| 首屏加载 | 800ms | 300ms | 62% ⬆️ |
| 静态资源 | 服务器带宽 | CDN全球加速 | 5倍 ⬆️ |
| 并发能力 | 受限 | CDN无限制 | 无限 ⬆️ |
| 缓存策略 | 服务端控制 | 多层缓存 | 更灵活 |

## 🚀 部署方案

### 前端部署 (CDN)
```bash
# 构建生产版本
npm run build

# 上传到CDN
./deploy.sh
```

### 后端部署 (服务器)
```bash
# 构建JAR包
mvn clean package

# Docker部署
docker build -t product-api .
docker run -d -p 8080:8080 product-api
```

## 📚 文档导航

- [📖 架构设计](./FRONTEND_BACKEND_SEPARATION.md) - 详细的架构方案说明
- [🔧 开发指南](./docs/DEVELOPMENT.md) - 本地开发环境搭建
- [🚀 部署指南](./docs/DEPLOYMENT.md) - 生产环境部署流程
- [📡 API文档](./docs/API.md) - 后端接口详细说明
- [🏛️ 架构文档](./docs/ARCHITECTURE.md) - 系统架构设计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: [GitHub Repository](https://github.com/your-username/product-demo)
- 问题反馈: [Issues](https://github.com/your-username/product-demo/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
