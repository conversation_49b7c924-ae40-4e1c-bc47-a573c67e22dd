# 🛍️ 现代化商品展示系统

> 基于前后端分离架构的现代化电商展示应用，支持CDN静态文件分发

## 📋 项目概述

这是一个采用前后端分离架构的现代化商品展示系统，前端使用React+Vite构建纯静态应用，后端使用Java SpringBoot提供RESTful API服务。

### 🎯 核心特性

- ✅ **前后端完全分离** - 前端静态文件可部署到CDN
- ✅ **现代化技术栈** - React 18 + Vite + TypeScript + Tailwind CSS
- ✅ **高性能缓存** - React Query + Redis多层缓存
- ✅ **移动端优化** - 响应式设计，PWA支持
- ✅ **类型安全** - 前后端TypeScript/Java完整类型定义
- ✅ **企业级后端** - SpringBoot + MySQL + Redis
- ✅ **开发体验** - 热重载、自动化部署、完整文档

## 🏗️ 项目结构

```
📦 商品展示系统
├── 📁 vite-frontend/          # 前端应用 (React + Vite)
│   ├── 📁 src/
│   │   ├── 📁 components/     # 可复用组件
│   │   ├── 📁 pages/          # 页面组件
│   │   ├── 📁 hooks/          # 自定义Hooks
│   │   ├── 📁 services/       # API服务层
│   │   ├── 📁 store/          # 状态管理
│   │   └── 📁 types/          # TypeScript类型定义
│   ├── 📄 package.json        # 前端依赖配置
│   ├── 📄 vite.config.ts      # Vite构建配置
│   └── 📄 deploy.sh           # 前端部署脚本
├── 📁 springboot-backend/     # 后端API服务 (SpringBoot)
│   ├── 📁 src/main/java/
│   │   └── 📁 com/example/productapi/
│   │       ├── 📁 controller/ # REST控制器
│   │       ├── 📁 service/    # 业务逻辑层
│   │       ├── 📁 repository/ # 数据访问层
│   │       ├── 📁 entity/     # 数据实体
│   │       └── 📁 dto/        # 数据传输对象
│   └── 📄 pom.xml             # Maven依赖配置
├── 📁 docs/                   # 项目文档
│   ├── 📄 API.md              # API接口文档
│   ├── 📄 DEPLOYMENT.md       # 部署指南
│   ├── 📄 DEVELOPMENT.md      # 开发指南
│   └── 📄 ARCHITECTURE.md     # 架构设计文档
├── 📄 README.md               # 项目说明 (当前文件)
└── 📄 FRONTEND_BACKEND_SEPARATION.md # 架构方案详解
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **Java** >= 17
- **Maven** >= 3.8.0
- **MySQL** >= 8.0
- **Redis** >= 6.0

### 1. 启动前端开发服务器

```bash
# 进入前端目录
cd vite-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问地址: http://localhost:3000
```

### 2. 启动后端API服务

```bash
# 进入后端目录
cd springboot-backend

# 启动SpringBoot应用
mvn spring-boot:run

# API地址: http://localhost:8080
```

### 3. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE product_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'product_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON product_db.* TO 'product_user'@'localhost';
```

## 📱 功能展示

### 🏠 商品列表页
- 响应式网格布局
- 实时搜索功能
- 分类筛选
- 商品卡片展示
- 购物车功能
- 无限滚动加载

### 📦 商品详情页
- 商品图片轮播
- 详细信息展示
- 规格参数表格
- 用户评价
- 相关推荐
- 加入购物车

### 🛒 购物车功能
- 商品数量管理
- 价格计算
- 本地存储
- 同步到服务器

## 🛠️ 技术栈

### 前端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| React | 18.2+ | 用户界面库 |
| Vite | 5.0+ | 构建工具 |
| TypeScript | 5.0+ | 类型安全 |
| Tailwind CSS | 3.3+ | 原子化CSS |
| React Router | 6.20+ | 客户端路由 |
| React Query | 5.8+ | 服务端状态管理 |
| Zustand | 4.4+ | 客户端状态管理 |
| Axios | 1.6+ | HTTP客户端 |

### 后端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Spring Boot | 3.2+ | Java框架 |
| Spring Security | 6.0+ | 安全框架 |
| Spring Data JPA | 3.2+ | 数据访问 |
| MySQL | 8.0+ | 关系型数据库 |
| Redis | 6.0+ | 缓存数据库 |
| Maven | 3.8+ | 构建工具 |

## 📊 性能优势

| 指标 | 传统SSR | 当前方案 | 提升 |
|------|---------|----------|------|
| 首屏加载 | 800ms | 300ms | 62% ⬆️ |
| 静态资源 | 服务器带宽 | CDN全球加速 | 5倍 ⬆️ |
| 并发能力 | 受限 | CDN无限制 | 无限 ⬆️ |
| 缓存策略 | 服务端控制 | 多层缓存 | 更灵活 |

## 🚀 部署方案

### 前端部署 (CDN)
```bash
# 构建生产版本
npm run build

# 上传到CDN
./deploy.sh
```

### 后端部署 (服务器)
```bash
# 构建JAR包
mvn clean package

# Docker部署
docker build -t product-api .
docker run -d -p 8080:8080 product-api
```

## 📚 文档导航

- [📖 架构设计](./FRONTEND_BACKEND_SEPARATION.md) - 详细的架构方案说明
- [🔧 开发指南](./docs/DEVELOPMENT.md) - 本地开发环境搭建
- [🚀 部署指南](./docs/DEPLOYMENT.md) - 生产环境部署流程
- [📡 API文档](./docs/API.md) - 后端接口详细说明
- [🏛️ 架构文档](./docs/ARCHITECTURE.md) - 系统架构设计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: [GitHub Repository](https://github.com/your-username/product-demo)
- 问题反馈: [Issues](https://github.com/your-username/product-demo/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
