version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: product-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: product_db
      MYSQL_USER: product_user
      MYSQL_PASSWORD: your_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - product-network

  # Redis缓存
  redis:
    image: redis:6.0-alpine
    container_name: product-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - product-network

  # SpringBoot后端API
  backend:
    build: ./springboot-backend
    container_name: product-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: *****************************************************************************************
      SPRING_DATASOURCE_USERNAME: product_user
      SPRING_DATASOURCE_PASSWORD: your_password
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - product-network
    restart: unless-stopped

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: product-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./vite-frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
    networks:
      - product-network

volumes:
  mysql_data:
  redis_data:

networks:
  product-network:
    driver: bridge
