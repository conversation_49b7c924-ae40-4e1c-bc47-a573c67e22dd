# 前端技术详解

## 技术栈选择理由

### React 18 + TypeScript
- **React 18**: 提供并发特性、自动批处理、Suspense等现代化功能
- **TypeScript**: 提供静态类型检查，减少运行时错误，提升开发效率
- **组合优势**: 类型安全的组件开发，更好的IDE支持和重构能力

### Vite 构建工具
```javascript
// vite.config.ts 核心配置
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'), // 路径别名
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080', // 代理后端API
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

**Vite优势:**
- ⚡ 极速的冷启动
- 🔥 热模块替换(HMR)
- 📦 优化的构建输出
- 🛠️ 丰富的插件生态

### Tailwind CSS 样式方案
```css
/* 实用优先的CSS框架 */
.product-card {
  @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
}
```

**Tailwind优势:**
- 🎨 一致的设计系统
- 📱 响应式设计支持
- 🚀 更小的CSS包体积
- 🔧 高度可定制

## 核心架构设计

### 1. 组件架构
```
components/
├── ui/                    # 基础UI组件
│   ├── Button.tsx        # 按钮组件
│   ├── Input.tsx         # 输入框组件
│   ├── Card.tsx          # 卡片组件
│   ├── Loading.tsx       # 加载组件
│   └── Modal.tsx         # 模态框组件
├── ProductCard.tsx       # 商品卡片
├── ProductGrid.tsx       # 商品网格
├── SearchBar.tsx         # 搜索栏
└── Header.tsx           # 页面头部
```

**设计原则:**
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 基础组件可在多处使用
- **组合优于继承**: 通过组合构建复杂组件

### 2. 状态管理 - Zustand
```typescript
// cartStore.ts - 购物车状态管理
export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      totalItems: 0,
      totalPrice: 0,
      
      addItem: (product: Product, quantity: number = 1) => {
        // 添加商品逻辑
      },
      
      removeItem: (productId: number) => {
        // 移除商品逻辑
      }
    }),
    {
      name: 'cart-storage', // localStorage持久化
    }
  )
);
```

**Zustand优势:**
- 🪶 轻量级 (2.9kb gzipped)
- 🔄 支持持久化
- 🎯 TypeScript友好
- 🚫 无需Provider包装

### 3. 数据获取 - React Query
```typescript
// useProducts.ts - 商品数据Hook
export const useProducts = (params?: ProductSearchParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productApi.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    gcTime: 10 * 60 * 1000,   // 10分钟垃圾回收
  });
};

// 无限滚动
export const useInfiniteProducts = (params?: ProductSearchParams) => {
  return useInfiniteQuery({
    queryKey: ['infiniteProducts', params],
    queryFn: ({ pageParam = 0 }) => 
      productApi.getProducts({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.last ? undefined : lastPage.number + 1;
    },
  });
};
```

**React Query优势:**
- 🔄 自动缓存和同步
- 🔃 后台重新获取
- ⚡ 乐观更新
- 📡 离线支持

## 关键功能实现

### 1. 商品搜索与筛选
```typescript
// SearchBar.tsx - 防抖搜索
const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [keyword, setKeyword] = useState('');
  
  // 防抖搜索，300ms延迟
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onSearch(value.trim());
    }, 300),
    [onSearch]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
    debouncedSearch(value);
  };
  
  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2" />
      <input
        value={keyword}
        onChange={handleInputChange}
        className="input pl-10 pr-10 w-full"
        placeholder="搜索商品..."
      />
    </div>
  );
};
```

### 2. 响应式商品网格
```typescript
// ProductGrid.tsx - 自适应网格布局
const ProductGrid: React.FC<ProductGridProps> = ({ products, loading }) => {
  if (loading) {
    return (
      <div className="product-grid">
        {Array.from({ length: 12 }).map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};
```

### 3. 购物车功能
```typescript
// ProductCard.tsx - 购物车交互
const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { addItem, isInCart, getItemQuantity } = useCartStore();
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    addItem(product);
  };

  const inCart = isInCart(product.id);
  const quantity = getItemQuantity(product.id);

  return (
    <Card className="product-card">
      {/* 商品信息 */}
      <Button
        onClick={handleAddToCart}
        disabled={product.stock === 0}
        variant={inCart ? "secondary" : "primary"}
      >
        <ShoppingCart className="w-4 h-4 mr-2" />
        {product.stock === 0 
          ? '缺货' 
          : inCart 
            ? `已添加 (${quantity})` 
            : '加入购物车'
        }
      </Button>
    </Card>
  );
};
```

## 性能优化策略

### 1. 代码分割
```typescript
// App.tsx - 路由级代码分割
const HomePage = lazy(() => import('./pages/HomePage'));
const ProductDetailPage = lazy(() => import('./pages/ProductDetailPage'));

function App() {
  return (
    <Suspense fallback={<PageLoading />}>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/products/:id" element={<ProductDetailPage />} />
      </Routes>
    </Suspense>
  );
}
```

### 2. 图片懒加载
```typescript
// ProductCard.tsx - 图片优化
<img
  src={product.imageUrl}
  alt={product.name}
  className="w-full h-48 object-cover"
  loading="lazy" // 原生懒加载
  onError={(e) => {
    e.currentTarget.src = getImagePlaceholder(400, 300);
  }}
/>
```

### 3. 虚拟化长列表
```typescript
// 对于大量数据，可以使用react-window
import { FixedSizeList as List } from 'react-window';

const VirtualizedProductList = ({ products }) => (
  <List
    height={600}
    itemCount={products.length}
    itemSize={200}
    itemData={products}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <ProductCard product={data[index]} />
      </div>
    )}
  </List>
);
```

## 开发工具配置

### 1. ESLint + Prettier
```json
// .eslintrc.json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "react-hooks/exhaustive-deps": "warn",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```

### 2. TypeScript配置
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

## 测试策略

### 1. 单元测试
```typescript
// ProductCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import ProductCard from './ProductCard';

describe('ProductCard', () => {
  const mockProduct = {
    id: 1,
    name: 'Test Product',
    price: 99.99,
    // ...其他属性
  };

  test('renders product information', () => {
    render(<ProductCard product={mockProduct} />);
    
    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('¥99.99')).toBeInTheDocument();
  });

  test('adds product to cart when button clicked', () => {
    render(<ProductCard product={mockProduct} />);
    
    const addButton = screen.getByText('加入购物车');
    fireEvent.click(addButton);
    
    // 验证购物车状态更新
  });
});
```

### 2. 集成测试
```typescript
// HomePage.integration.test.tsx
describe('HomePage Integration', () => {
  test('loads and displays products', async () => {
    render(<HomePage />);
    
    // 等待产品加载
    await waitFor(() => {
      expect(screen.getByText('iPhone 15 Pro')).toBeInTheDocument();
    });
    
    // 测试搜索功能
    const searchInput = screen.getByPlaceholderText('搜索商品...');
    fireEvent.change(searchInput, { target: { value: 'iPhone' } });
    
    await waitFor(() => {
      expect(screen.getByText('iPhone 15 Pro')).toBeInTheDocument();
    });
  });
});
```

## 部署优化

### 1. 构建优化
```typescript
// vite.config.ts - 生产构建优化
export default defineConfig({
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          query: ['@tanstack/react-query'],
        }
      }
    }
  }
});
```

### 2. CDN部署
```bash
# 构建脚本
npm run build

# 部署到CDN
aws s3 sync dist/ s3://your-bucket-name --delete
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

这个前端架构提供了现代化、高性能、可维护的解决方案，适合中大型电商项目的需求。
