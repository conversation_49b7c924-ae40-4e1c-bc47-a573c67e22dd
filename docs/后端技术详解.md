# 后端技术详解

## 技术栈选择理由

### Spring Boot 2.7.18
- **企业级框架**: 成熟稳定，广泛应用于生产环境
- **自动配置**: 减少样板代码，快速启动项目
- **生态丰富**: 完整的Spring生态系统支持
- **微服务友好**: 天然支持微服务架构

### Spring Data JPA
```java
// ProductRepository.java - 强大的数据访问抽象
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    // 方法名查询 - 自动生成SQL
    Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable);
    Page<Product> findByCategory(String category, Pageable pageable);
    Page<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    // 自定义查询 - 复杂业务逻辑
    @Query("SELECT p FROM Product p WHERE " +
           "(:keyword IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:category IS NULL OR p.category = :category) AND " +
           "p.stock > 0")
    Page<Product> searchProducts(@Param("keyword") String keyword,
                                @Param("category") String category,
                                Pageable pageable);
}
```

**JPA优势:**
- 🔄 对象关系映射(ORM)
- 📝 声明式事务管理
- 🔍 强大的查询能力
- 📊 自动分页和排序

### 数据库设计

#### 1. 实体关系设计
```java
// Product.java - 商品实体
@Entity
@Table(name = "products")
@EntityListeners(AuditingEntityListener.class)
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 255)
    @Column(nullable = false)
    private String name;
    
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.0", inclusive = false)
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    // 一对多关系 - 商品图片
    @ElementCollection
    @CollectionTable(name = "product_images", 
                    joinColumns = @JoinColumn(name = "product_id"))
    @Column(name = "image_url")
    private List<String> images;
    
    // 键值对存储 - 商品规格
    @ElementCollection
    @CollectionTable(name = "product_specifications",
                    joinColumns = @JoinColumn(name = "product_id"))
    @MapKeyColumn(name = "spec_key")
    @Column(name = "spec_value")
    private Map<String, String> specifications;
    
    // 审计字段 - 自动管理创建和更新时间
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(nullable = false)
    private LocalDateTime updatedAt;
}
```

#### 2. 数据验证
```java
// ProductDTO.java - 数据传输对象
public class ProductDTO {
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 255, message = "商品名称长度不能超过255个字符")
    private String name;
    
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "商品价格必须大于0")
    private BigDecimal price;
    
    @Min(value = 0, message = "库存数量不能小于0")
    private Integer stock;
    
    @DecimalMin(value = "0.0", message = "评分不能小于0")
    @DecimalMax(value = "5.0", message = "评分不能大于5")
    private BigDecimal rating;
}
```

## 服务层架构

### 1. 业务逻辑封装
```java
// ProductService.java - 业务服务层
@Service
@Transactional
public class ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    // 只读事务 - 提升性能
    @Transactional(readOnly = true)
    @Cacheable(value = "products", key = "#pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<ProductDTO> getAllProducts(Pageable pageable) {
        Page<Product> products = productRepository.findAll(pageable);
        return products.map(this::convertToDTO);
    }
    
    // 缓存热门商品
    @Transactional(readOnly = true)
    @Cacheable(value = "popularProducts", key = "#limit")
    public List<ProductDTO> getPopularProducts(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Product> products = productRepository.findPopularProducts(pageable);
        return products.stream()
                      .map(this::convertToDTO)
                      .collect(Collectors.toList());
    }
    
    // 智能推荐算法
    @Transactional(readOnly = true)
    @Cacheable(value = "recommendedProducts", key = "#productId + '_' + #limit")
    public List<ProductDTO> getRecommendedProducts(Long productId, int limit) {
        Optional<Product> product = productRepository.findById(productId);
        if (product.isPresent()) {
            String category = product.get().getCategory();
            Pageable pageable = PageRequest.of(0, limit);
            // 基于同分类的协同过滤推荐
            List<Product> products = productRepository
                .findRecommendedProducts(category, productId, pageable);
            return products.stream()
                          .map(this::convertToDTO)
                          .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
```

### 2. 缓存策略
```java
// CacheConfig.java - 缓存配置
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10)) // 10分钟过期
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                               .cacheDefaults(config)
                               .build();
    }
}
```

## RESTful API设计

### 1. 控制器层
```java
// ProductController.java - REST控制器
@RestController
@RequestMapping("/products")
@CrossOrigin(origins = "*")
@Tag(name = "商品管理", description = "商品相关的API接口")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @GetMapping
    @Operation(summary = "获取商品列表", description = "分页获取商品列表，支持排序")
    public ResponseEntity<Page<ProductDTO>> getAllProducts(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder) {
        
        Pageable pageable = PageRequest.of(page, size, 
            ProductService.createSort(sortBy, sortOrder));
        Page<ProductDTO> products = productService.getAllProducts(pageable);
        return ResponseEntity.ok(products);
    }
    
    @GetMapping("/search")
    @Operation(summary = "搜索商品", description = "根据关键词和筛选条件搜索商品")
    public ResponseEntity<Page<ProductDTO>> searchProducts(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "商品分类") @RequestParam(required = false) String category,
            @Parameter(description = "最低价格") @RequestParam(required = false) BigDecimal minPrice,
            @Parameter(description = "最高价格") @RequestParam(required = false) BigDecimal maxPrice,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<ProductDTO> products = productService.searchProducts(
            keyword, category, null, minPrice, maxPrice, null, pageable);
        return ResponseEntity.ok(products);
    }
}
```

### 2. API文档生成
```java
// OpenAPI配置
@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("商品展示系统API")
                .version("1.0.0")
                .description("现代化商品展示系统的后端API文档")
                .contact(new Contact()
                    .name("开发团队")
                    .email("<EMAIL>")))
            .servers(Arrays.asList(
                new Server().url("http://localhost:8080").description("开发环境"),
                new Server().url("https://api.example.com").description("生产环境")
            ));
    }
}
```

## 数据初始化

### 1. 启动时数据初始化
```java
// DataInitializer.java - 数据初始化
@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Override
    public void run(String... args) throws Exception {
        initializeCategories();
        initializeProducts();
    }
    
    private void initializeCategories() {
        if (categoryRepository.count() == 0) {
            List<Category> categories = Arrays.asList(
                new Category("电子产品", "electronics", "各类电子设备和数码产品"),
                new Category("服装鞋帽", "clothing", "时尚服装和鞋帽配饰"),
                new Category("家居用品", "home", "家居装饰和生活用品")
            );
            categoryRepository.saveAll(categories);
        }
    }
    
    private void initializeProducts() {
        if (productRepository.count() == 0) {
            List<Product> products = createSampleProducts();
            productRepository.saveAll(products);
        }
    }
    
    private Product createProduct(String name, String description, 
                                 BigDecimal price, BigDecimal originalPrice,
                                 String imageUrl, String category, 
                                 String brand, Integer stock) {
        Product product = new Product(name, description, price, imageUrl, 
                                    category, brand, stock);
        product.setOriginalPrice(originalPrice);
        product.setRating(new BigDecimal("4.5"));
        product.setReviewCount(100);
        
        // 添加规格参数
        Map<String, String> specifications = new HashMap<>();
        specifications.put("保修期", "1年");
        specifications.put("产地", "中国");
        product.setSpecifications(specifications);
        
        // 添加标签
        product.setTags(Arrays.asList("热销", "推荐"));
        
        return product;
    }
}
```

## 配置管理

### 1. 应用配置
```yaml
# application.yml - 分环境配置
server:
  port: 8080

spring:
  application:
    name: product-api
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # Redis缓存配置
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# 日志配置
logging:
  level:
    com.example.productapi: DEBUG
    org.springframework.web: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: **************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: product_user
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
```

### 2. CORS配置
```java
// CorsConfig.java - 跨域配置
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
    }
}
```

## 性能优化

### 1. 数据库优化
```java
// 索引优化
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_category", columnList = "category"),
    @Index(name = "idx_brand", columnList = "brand"),
    @Index(name = "idx_price", columnList = "price"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
public class Product {
    // 实体定义
}

// 查询优化
@Query("SELECT p FROM Product p " +
       "LEFT JOIN FETCH p.images " +
       "LEFT JOIN FETCH p.specifications " +
       "WHERE p.id = :id")
Optional<Product> findByIdWithDetails(@Param("id") Long id);
```

### 2. 缓存策略
```java
// 多级缓存
@Service
public class ProductService {
    
    // L1缓存 - 应用级缓存
    @Cacheable(value = "products", key = "#id")
    public ProductDTO getProductById(Long id) {
        return productRepository.findById(id)
                               .map(this::convertToDTO)
                               .orElse(null);
    }
    
    // L2缓存 - Redis分布式缓存
    @Cacheable(value = "popularProducts", key = "#limit", 
               unless = "#result.size() < #limit")
    public List<ProductDTO> getPopularProducts(int limit) {
        // 实现逻辑
    }
}
```

## 监控和运维

### 1. 健康检查
```java
// HealthIndicator - 自定义健康检查
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Override
    public Health health() {
        try {
            long count = productRepository.count();
            return Health.up()
                        .withDetail("database", "Available")
                        .withDetail("productCount", count)
                        .build();
        } catch (Exception e) {
            return Health.down()
                        .withDetail("database", "Unavailable")
                        .withException(e)
                        .build();
        }
    }
}
```

### 2. 指标监控
```yaml
# application.yml - Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

## 安全考虑

### 1. 输入验证
```java
// 全局异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(
            MethodArgumentNotValidException ex) {
        
        List<String> errors = ex.getBindingResult()
                               .getFieldErrors()
                               .stream()
                               .map(FieldError::getDefaultMessage)
                               .collect(Collectors.toList());
        
        ErrorResponse errorResponse = new ErrorResponse("验证失败", errors);
        return ResponseEntity.badRequest().body(errorResponse);
    }
}
```

### 2. SQL注入防护
```java
// 使用参数化查询防止SQL注入
@Query("SELECT p FROM Product p WHERE " +
       "LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
       "LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
Page<Product> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
```

这个后端架构提供了企业级的可靠性、性能和可维护性，适合生产环境部署。
