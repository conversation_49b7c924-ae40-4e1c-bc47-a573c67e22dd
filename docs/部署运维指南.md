# 部署运维指南

## 开发环境搭建

### 1. 环境要求
```bash
# 检查环境版本
node --version    # >= 16.0.0
java -version     # >= 8 (推荐 11+)
mvn --version     # >= 3.6.0
git --version     # >= 2.0.0
```

### 2. 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd aug_pop

# 2. 启动后端服务
cd springboot-backend
mvn spring-boot:run

# 3. 启动前端服务 (新终端)
cd vite-frontend
npm install
npm run dev

# 4. 访问应用
# 前端: http://localhost:3000
# 后端: http://localhost:8080
# API文档: http://localhost:8080/swagger-ui.html
```

### 3. 开发工具配置
```json
// .vscode/settings.json - VSCode配置
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.compile.nullAnalysis.mode": "automatic"
}
```

## Docker容器化部署

### 1. 前端Dockerfile
```dockerfile
# vite-frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产镜像
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 后端Dockerfile
```dockerfile
# springboot-backend/Dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

# 复制JAR文件
COPY target/product-api-*.jar app.jar

# 创建非root用户
RUN addgroup --system spring && adduser --system spring --ingroup spring
USER spring:spring

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 3. Docker Compose编排
```yaml
# docker-compose.yml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: product-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: product_db
      MYSQL_USER: product_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - product-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: product-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - product-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 后端API服务
  backend:
    build: 
      context: ./springboot-backend
      dockerfile: Dockerfile
    container_name: product-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ******************************************************************
      SPRING_DATASOURCE_USERNAME: product_user
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD}
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - product-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  frontend:
    build:
      context: ./vite-frontend
      dockerfile: Dockerfile
    container_name: product-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - product-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  product-network:
    driver: bridge
```

### 4. 环境变量配置
```bash
# .env - 环境变量文件
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_PASSWORD=your_password
REDIS_PASSWORD=your_redis_password

# 生产环境额外配置
JWT_SECRET=your_jwt_secret
API_BASE_URL=https://api.yourdomain.com
CDN_BASE_URL=https://cdn.yourdomain.com
```

## 生产环境部署

### 1. 云服务器部署
```bash
# 服务器初始化脚本
#!/bin/bash

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Nginx (反向代理)
sudo apt install nginx -y

# 配置防火墙
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. Nginx反向代理配置
```nginx
# /etc/nginx/sites-available/product-app
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 前端静态资源
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:8080/actuator/health;
        access_log off;
    }
}
```

### 3. SSL证书配置
```bash
# 使用Let's Encrypt免费SSL证书
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 应用监控
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - monitoring

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - monitoring

  # 日志收集
  loki:
    image: grafana/loki:latest
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
```

### 2. 日志配置
```yaml
# application-prod.yml - 生产环境日志配置
logging:
  level:
    root: INFO
    com.example.productapi: INFO
    org.springframework.web: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/product-api/application.log
    max-size: 100MB
    max-history: 30
```

### 3. 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

# 检查服务状态
check_service() {
    local service_name=$1
    local url=$2
    
    echo "检查 $service_name..."
    
    if curl -f -s "$url" > /dev/null; then
        echo "✅ $service_name 正常"
        return 0
    else
        echo "❌ $service_name 异常"
        return 1
    fi
}

# 检查各个服务
check_service "前端服务" "http://localhost:80"
check_service "后端API" "http://localhost:8080/actuator/health"
check_service "数据库" "http://localhost:8080/actuator/health/db"
check_service "Redis缓存" "http://localhost:8080/actuator/health/redis"

# 检查磁盘空间
echo "检查磁盘空间..."
df -h | grep -E "/$|/var" | awk '{
    if ($5+0 > 80) {
        print "⚠️  磁盘使用率过高: " $5 " " $6
    } else {
        print "✅ 磁盘空间正常: " $5 " " $6
    }
}'

# 检查内存使用
echo "检查内存使用..."
free -h | awk 'NR==2{
    if ($3/$2*100 > 80) {
        print "⚠️  内存使用率过高: " $3 "/" $2
    } else {
        print "✅ 内存使用正常: " $3 "/" $2
    }
}'
```

## 备份和恢复

### 1. 数据库备份
```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="product_db_backup_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec product-mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD product_db > $BACKUP_DIR/$BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_DIR/$BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "数据库备份完成: $BACKUP_FILE.gz"
```

### 2. 应用备份
```bash
#!/bin/bash
# backup-application.sh

BACKUP_DIR="/backup/application"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份应用代码
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    --exclude=node_modules \
    --exclude=target \
    --exclude=.git \
    /path/to/aug_pop

# 备份配置文件
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz \
    /etc/nginx/sites-available/product-app \
    /etc/letsencrypt/live/yourdomain.com \
    .env

echo "应用备份完成"
```

## 性能优化

### 1. 数据库优化
```sql
-- 创建索引优化查询性能
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_brand ON products(brand);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_rating ON products(rating);

-- 复合索引优化复杂查询
CREATE INDEX idx_products_category_price ON products(category, price);
CREATE INDEX idx_products_brand_rating ON products(brand, rating);
```

### 2. 缓存优化
```yaml
# Redis配置优化
redis:
  # 内存优化
  maxmemory: 2gb
  maxmemory-policy: allkeys-lru
  
  # 持久化配置
  save: "900 1 300 10 60 10000"
  
  # 网络优化
  tcp-keepalive: 300
  timeout: 0
```

### 3. JVM优化
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms2g -Xmx4g \
           -XX:+UseG1GC \
           -XX:MaxGCPauseMillis=200 \
           -XX:+HeapDumpOnOutOfMemoryError \
           -XX:HeapDumpPath=/var/log/product-api/ \
           -Dspring.profiles.active=prod"
```

这个部署运维指南提供了从开发到生产的完整部署流程，确保应用的稳定性和可维护性。
