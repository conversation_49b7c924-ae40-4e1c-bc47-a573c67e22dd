# 现代化商品展示系统 - 项目说明文档

## 项目概述

这是一个基于现代化技术栈构建的电商商品展示系统，采用前后端分离架构，提供高性能、响应式的用户体验。

### 技术栈

**前端技术栈：**
- **React 18** - 现代化的前端框架，支持并发特性
- **TypeScript** - 提供类型安全和更好的开发体验
- **Vite** - 极速的构建工具，支持热更新
- **Tailwind CSS** - 实用优先的CSS框架
- **React Query (TanStack Query)** - 强大的数据获取和缓存库
- **Zustand** - 轻量级的状态管理库
- **React Router** - 客户端路由管理
- **Axios** - HTTP客户端库
- **Lucide React** - 现代化的图标库

**后端技术栈：**
- **Spring Boot 2.7.18** - 企业级Java框架
- **Spring Data JPA** - 数据访问层抽象
- **H2 Database** - 内存数据库（开发环境）
- **MySQL** - 生产环境数据库
- **Spring Cache + Redis** - 缓存解决方案
- **SpringDoc OpenAPI** - API文档生成
- **Maven** - 项目构建和依赖管理

### 项目特色

1. **现代化架构** - 采用微服务架构思想，前后端完全分离
2. **响应式设计** - 完美适配桌面端、平板和移动设备
3. **高性能** - 使用缓存、懒加载、代码分割等优化技术
4. **类型安全** - 前端使用TypeScript，后端使用强类型Java
5. **开发友好** - 热更新、自动重启、完善的错误处理
6. **可扩展性** - 模块化设计，易于扩展新功能

## 项目结构

```
aug_pop/
├── vite-frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/           # 可复用组件
│   │   │   ├── ui/              # 基础UI组件
│   │   │   ├── ProductCard.tsx  # 商品卡片组件
│   │   │   ├── ProductGrid.tsx  # 商品网格组件
│   │   │   ├── SearchBar.tsx    # 搜索栏组件
│   │   │   └── Header.tsx       # 页面头部组件
│   │   ├── pages/               # 页面组件
│   │   │   ├── HomePage.tsx     # 首页
│   │   │   └── ProductDetailPage.tsx # 商品详情页
│   │   ├── hooks/               # 自定义Hook
│   │   │   ├── useProducts.ts   # 商品相关Hook
│   │   │   └── useCategories.ts # 分类相关Hook
│   │   ├── services/            # API服务
│   │   │   └── api.ts          # API接口定义
│   │   ├── store/               # 状态管理
│   │   │   └── cartStore.ts    # 购物车状态
│   │   ├── types/               # 类型定义
│   │   │   └── index.ts        # 全局类型
│   │   ├── utils/               # 工具函数
│   │   │   └── index.ts        # 通用工具
│   │   ├── App.tsx             # 应用根组件
│   │   ├── main.tsx            # 应用入口
│   │   └── index.css           # 全局样式
│   ├── package.json            # 依赖配置
│   ├── vite.config.ts          # Vite配置
│   ├── tailwind.config.js      # Tailwind配置
│   └── tsconfig.json           # TypeScript配置
├── springboot-backend/           # 后端项目
│   ├── src/main/java/com/example/productapi/
│   │   ├── controller/         # 控制器层
│   │   │   └── ProductController.java
│   │   ├── service/            # 服务层
│   │   │   └── ProductService.java
│   │   ├── repository/         # 数据访问层
│   │   │   ├── ProductRepository.java
│   │   │   └── CategoryRepository.java
│   │   ├── entity/             # 实体类
│   │   │   ├── Product.java
│   │   │   └── Category.java
│   │   ├── dto/                # 数据传输对象
│   │   │   └── ProductDTO.java
│   │   ├── config/             # 配置类
│   │   │   ├── CorsConfig.java
│   │   │   └── DataInitializer.java
│   │   └── ProductApiApplication.java # 启动类
│   ├── src/main/resources/
│   │   └── application.yml     # 应用配置
│   ├── pom.xml                 # Maven配置
│   └── Dockerfile              # Docker配置
├── docker-compose.yml          # Docker编排配置
├── init.sql                    # 数据库初始化脚本
└── README.md                   # 项目说明
```

## 核心功能

### 1. 商品展示
- 商品列表展示，支持分页
- 商品详情页面，包含图片、规格、评价等
- 商品搜索和筛选功能
- 分类浏览功能

### 2. 购物车功能
- 添加商品到购物车
- 购物车状态持久化
- 购物车商品数量管理

### 3. 数据管理
- 商品信息的CRUD操作
- 分类管理
- 库存管理
- 评价系统

### 4. 性能优化
- 前端组件懒加载
- API响应缓存
- 图片懒加载
- 代码分割

## 开发环境要求

- **Node.js** >= 16.0.0
- **Java** >= 8 (推荐Java 11+)
- **Maven** >= 3.6.0
- **Git** >= 2.0.0

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd aug_pop
```

### 2. 启动后端服务
```bash
cd springboot-backend
mvn spring-boot:run
```
后端服务将在 http://localhost:8080 启动

### 3. 启动前端服务
```bash
cd vite-frontend
npm install
npm run dev
```
前端服务将在 http://localhost:3000 启动

### 4. 访问应用
打开浏览器访问 http://localhost:3000

## API文档

后端提供了完整的RESTful API，启动后端服务后可以通过以下地址访问API文档：
- Swagger UI: http://localhost:8080/swagger-ui.html
- API Docs: http://localhost:8080/api-docs

### 主要API端点

- `GET /products` - 获取商品列表
- `GET /products/{id}` - 获取商品详情
- `GET /products/search` - 搜索商品
- `GET /products/popular` - 获取热门商品
- `GET /products/recommended` - 获取推荐商品
- `GET /categories` - 获取分类列表

## 部署说明

### Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

### 生产环境部署
1. 前端构建：`npm run build`
2. 将构建产物部署到CDN或静态服务器
3. 后端打包：`mvn clean package`
4. 部署JAR文件到服务器
5. 配置MySQL数据库
6. 配置Redis缓存

## 项目亮点

1. **现代化技术栈** - 使用最新的前端和后端技术
2. **完整的类型系统** - TypeScript + Java强类型保证
3. **优秀的用户体验** - 响应式设计，流畅的交互
4. **高性能** - 多级缓存，懒加载，代码分割
5. **可维护性** - 清晰的项目结构，完善的文档
6. **可扩展性** - 模块化设计，易于添加新功能
