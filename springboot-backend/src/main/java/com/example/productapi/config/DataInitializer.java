package com.example.productapi.config;

import com.example.productapi.entity.Category;
import com.example.productapi.entity.Product;
import com.example.productapi.repository.CategoryRepository;
import com.example.productapi.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Override
    public void run(String... args) throws Exception {
        // 初始化分类数据
        initializeCategories();
        
        // 初始化商品数据
        initializeProducts();
    }

    private void initializeCategories() {
        if (categoryRepository.count() == 0) {
            List<Category> categories = Arrays.asList(
                new Category("电子产品", "electronics", "各类电子设备和数码产品"),
                new Category("服装鞋帽", "clothing", "时尚服装和鞋帽配饰"),
                new Category("家居用品", "home", "家居装饰和生活用品"),
                new Category("图书音像", "books", "图书、音乐和影视产品"),
                new Category("运动户外", "sports", "运动器材和户外用品"),
                new Category("美妆护肤", "beauty", "化妆品和护肤用品")
            );
            categoryRepository.saveAll(categories);
        }
    }

    private void initializeProducts() {
        if (productRepository.count() == 0) {
            List<Product> products = Arrays.asList(
                createProduct("iPhone 15 Pro", "最新款iPhone，搭载A17 Pro芯片，拍照更清晰", 
                    new BigDecimal("7999.00"), new BigDecimal("8999.00"),
                    "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400", 
                    "电子产品", "Apple", 50, new BigDecimal("4.8"), 1250),
                
                createProduct("MacBook Air M2", "轻薄便携的笔记本电脑，性能强劲", 
                    new BigDecimal("8999.00"), new BigDecimal("9999.00"),
                    "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400", 
                    "电子产品", "Apple", 30, new BigDecimal("4.7"), 890),
                
                createProduct("Nike Air Max 270", "舒适透气的运动鞋，适合日常穿着", 
                    new BigDecimal("899.00"), new BigDecimal("1099.00"),
                    "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400", 
                    "服装鞋帽", "Nike", 100, new BigDecimal("4.5"), 2100),
                
                createProduct("Adidas Ultraboost 22", "专业跑步鞋，提供卓越的缓震效果", 
                    new BigDecimal("1299.00"), new BigDecimal("1499.00"),
                    "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400", 
                    "运动户外", "Adidas", 80, new BigDecimal("4.6"), 1560),
                
                createProduct("Sony WH-1000XM4", "降噪无线耳机，音质出色", 
                    new BigDecimal("2299.00"), new BigDecimal("2599.00"),
                    "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400", 
                    "电子产品", "Sony", 60, new BigDecimal("4.7"), 980),
                
                createProduct("Levi's 501 牛仔裤", "经典款牛仔裤，百搭时尚", 
                    new BigDecimal("599.00"), new BigDecimal("799.00"),
                    "https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400", 
                    "服装鞋帽", "Levi's", 120, new BigDecimal("4.3"), 3200),
                
                createProduct("IKEA 北欧风餐桌", "简约现代的餐桌，适合小户型", 
                    new BigDecimal("1299.00"), new BigDecimal("1599.00"),
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400", 
                    "家居用品", "IKEA", 25, new BigDecimal("4.4"), 450),
                
                createProduct("Kindle Paperwhite", "电子书阅读器，护眼显示屏", 
                    new BigDecimal("998.00"), new BigDecimal("1198.00"),
                    "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400", 
                    "图书音像", "Amazon", 40, new BigDecimal("4.6"), 1100),
                
                createProduct("YSL 口红", "经典色号，持久不脱色", 
                    new BigDecimal("320.00"), new BigDecimal("380.00"),
                    "https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400", 
                    "美妆护肤", "YSL", 200, new BigDecimal("4.8"), 5600),
                
                createProduct("Dyson V15 吸尘器", "无线吸尘器，强劲吸力", 
                    new BigDecimal("3999.00"), new BigDecimal("4499.00"),
                    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400", 
                    "家居用品", "Dyson", 15, new BigDecimal("4.7"), 320),
                
                createProduct("Tesla Model 3 车载充电器", "专用车载充电器，快速充电", 
                    new BigDecimal("299.00"), new BigDecimal("399.00"),
                    "https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400", 
                    "电子产品", "Tesla", 80, new BigDecimal("4.5"), 890),
                
                createProduct("Patagonia 冲锋衣", "户外防风防水外套", 
                    new BigDecimal("1899.00"), new BigDecimal("2299.00"),
                    "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400", 
                    "运动户外", "Patagonia", 35, new BigDecimal("4.6"), 670)
            );
            
            productRepository.saveAll(products);
        }
    }

    private Product createProduct(String name, String description, BigDecimal price, BigDecimal originalPrice,
                                 String imageUrl, String category, String brand, Integer stock, 
                                 BigDecimal rating, Integer reviewCount) {
        Product product = new Product(name, description, price, imageUrl, category, brand, stock);
        product.setOriginalPrice(originalPrice);
        product.setRating(rating);
        product.setReviewCount(reviewCount);
        
        // 添加一些示例规格参数
        Map<String, String> specifications = new HashMap<>();
        if ("电子产品".equals(category)) {
            specifications.put("保修期", "1年");
            specifications.put("产地", "中国");
        } else if ("服装鞋帽".equals(category)) {
            specifications.put("材质", "棉质");
            specifications.put("适用季节", "四季");
        }
        product.setSpecifications(specifications);
        
        // 添加一些标签
        product.setTags(Arrays.asList("热销", "推荐"));
        
        return product;
    }
}
