package com.example.productapi.controller;

import com.example.productapi.dto.ProductDTO;
import com.example.productapi.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/products")
@CrossOrigin(origins = "*")
@Tag(name = "商品管理", description = "商品相关的API接口")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping
    @Operation(summary = "获取商品列表", description = "分页获取商品列表，支持排序")
    public ResponseEntity<Page<ProductDTO>> getAllProducts(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder) {
        
        Pageable pageable = PageRequest.of(page, size, ProductService.createSort(sortBy, sortOrder));
        Page<ProductDTO> products = productService.getAllProducts(pageable);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取商品详情", description = "根据商品ID获取详细信息")
    public ResponseEntity<ProductDTO> getProductById(
            @Parameter(description = "商品ID") @PathVariable Long id) {
        
        Optional<ProductDTO> product = productService.getProductById(id);
        return product.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @Operation(summary = "搜索商品", description = "根据关键词和筛选条件搜索商品")
    public ResponseEntity<Page<ProductDTO>> searchProducts(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "商品分类") @RequestParam(required = false) String category,
            @Parameter(description = "商品品牌") @RequestParam(required = false) String brand,
            @Parameter(description = "最低价格") @RequestParam(required = false) BigDecimal minPrice,
            @Parameter(description = "最高价格") @RequestParam(required = false) BigDecimal maxPrice,
            @Parameter(description = "最低评分") @RequestParam(required = false) BigDecimal minRating,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder) {
        
        Pageable pageable = PageRequest.of(page, size, ProductService.createSort(sortBy, sortOrder));
        Page<ProductDTO> products = productService.searchProducts(
            keyword, category, brand, minPrice, maxPrice, minRating, pageable);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/popular")
    @Operation(summary = "获取热门商品", description = "获取热门商品列表")
    public ResponseEntity<List<ProductDTO>> getPopularProducts(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ProductDTO> products = productService.getPopularProducts(limit);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/recommended")
    @Operation(summary = "获取推荐商品", description = "根据商品ID获取推荐商品")
    public ResponseEntity<List<ProductDTO>> getRecommendedProducts(
            @Parameter(description = "商品ID") @RequestParam(required = false) Long productId,
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        if (productId != null) {
            List<ProductDTO> products = productService.getRecommendedProducts(productId, limit);
            return ResponseEntity.ok(products);
        } else {
            List<ProductDTO> products = productService.getLatestProducts(limit);
            return ResponseEntity.ok(products);
        }
    }

    @GetMapping("/latest")
    @Operation(summary = "获取最新商品", description = "获取最新上架的商品")
    public ResponseEntity<List<ProductDTO>> getLatestProducts(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ProductDTO> products = productService.getLatestProducts(limit);
        return ResponseEntity.ok(products);
    }

    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类获取商品", description = "获取指定分类下的商品")
    public ResponseEntity<Page<ProductDTO>> getProductsByCategory(
            @Parameter(description = "商品分类") @PathVariable String category,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder) {
        
        Pageable pageable = PageRequest.of(page, size, ProductService.createSort(sortBy, sortOrder));
        Page<ProductDTO> products = productService.getProductsByCategory(category, pageable);
        return ResponseEntity.ok(products);
    }

    @PostMapping
    @Operation(summary = "创建商品", description = "创建新的商品")
    public ResponseEntity<ProductDTO> createProduct(@Valid @RequestBody ProductDTO productDTO) {
        ProductDTO createdProduct = productService.createProduct(productDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdProduct);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新商品", description = "更新指定ID的商品信息")
    public ResponseEntity<ProductDTO> updateProduct(
            @Parameter(description = "商品ID") @PathVariable Long id,
            @Valid @RequestBody ProductDTO productDTO) {
        
        Optional<ProductDTO> updatedProduct = productService.updateProduct(id, productDTO);
        return updatedProduct.map(ResponseEntity::ok)
                            .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除商品", description = "删除指定ID的商品")
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "商品ID") @PathVariable Long id) {
        
        boolean deleted = productService.deleteProduct(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    @GetMapping("/categories")
    @Operation(summary = "获取所有分类", description = "获取系统中所有的商品分类")
    public ResponseEntity<List<String>> getAllCategories() {
        List<String> categories = productService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/brands")
    @Operation(summary = "获取所有品牌", description = "获取系统中所有的商品品牌")
    public ResponseEntity<List<String>> getAllBrands() {
        List<String> brands = productService.getAllBrands();
        return ResponseEntity.ok(brands);
    }
}
