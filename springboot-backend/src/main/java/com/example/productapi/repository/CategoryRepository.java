package com.example.productapi.repository;

import com.example.productapi.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    // 根据slug查找分类
    Optional<Category> findBySlug(String slug);

    // 查找顶级分类（没有父分类的分类）
    List<Category> findByParentIdIsNull();

    // 查找子分类
    List<Category> findByParentId(Long parentId);

    // 检查slug是否存在
    boolean existsBySlug(String slug);

    // 根据名称查找分类
    Optional<Category> findByName(String name);

    // 获取分类层级结构
    @Query("SELECT c FROM Category c LEFT JOIN FETCH c.children WHERE c.parentId IS NULL")
    List<Category> findCategoriesWithChildren();
}
