package com.example.productapi.repository;

import com.example.productapi.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    // 根据名称搜索商品
    Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable);

    // 根据分类查找商品
    Page<Product> findByCategory(String category, Pageable pageable);

    // 根据品牌查找商品
    Page<Product> findByBrand(String brand, Pageable pageable);

    // 根据价格区间查找商品
    Page<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);

    // 根据评分查找商品
    Page<Product> findByRatingGreaterThanEqual(BigDecimal rating, Pageable pageable);

    // 查找有库存的商品
    Page<Product> findByStockGreaterThan(Integer stock, Pageable pageable);

    // 复合搜索查询
    @Query("SELECT p FROM Product p WHERE " +
           "(:keyword IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:category IS NULL OR p.category = :category) AND " +
           "(:brand IS NULL OR p.brand = :brand) AND " +
           "(:minPrice IS NULL OR p.price >= :minPrice) AND " +
           "(:maxPrice IS NULL OR p.price <= :maxPrice) AND " +
           "(:minRating IS NULL OR p.rating >= :minRating) AND " +
           "p.stock > 0")
    Page<Product> searchProducts(@Param("keyword") String keyword,
                                @Param("category") String category,
                                @Param("brand") String brand,
                                @Param("minPrice") BigDecimal minPrice,
                                @Param("maxPrice") BigDecimal maxPrice,
                                @Param("minRating") BigDecimal minRating,
                                Pageable pageable);

    // 获取热门商品（按评分和评价数量排序）
    @Query("SELECT p FROM Product p WHERE p.stock > 0 ORDER BY p.rating DESC, p.reviewCount DESC")
    List<Product> findPopularProducts(Pageable pageable);

    // 获取推荐商品（同分类下的高评分商品）
    @Query("SELECT p FROM Product p WHERE p.category = :category AND p.id != :excludeId AND p.stock > 0 ORDER BY p.rating DESC, p.reviewCount DESC")
    List<Product> findRecommendedProducts(@Param("category") String category, 
                                         @Param("excludeId") Long excludeId, 
                                         Pageable pageable);

    // 获取最新商品
    @Query("SELECT p FROM Product p WHERE p.stock > 0 ORDER BY p.createdAt DESC")
    List<Product> findLatestProducts(Pageable pageable);

    // 获取所有分类
    @Query("SELECT DISTINCT p.category FROM Product p ORDER BY p.category")
    List<String> findAllCategories();

    // 获取所有品牌
    @Query("SELECT DISTINCT p.brand FROM Product p ORDER BY p.brand")
    List<String> findAllBrands();

    // 统计商品数量
    long countByCategory(String category);
    long countByBrand(String brand);
    long countByStockGreaterThan(Integer stock);
}
