package com.example.productapi.service;

import com.example.productapi.dto.ProductDTO;
import com.example.productapi.entity.Product;
import com.example.productapi.repository.ProductRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    // 获取所有商品（分页）
    @Transactional(readOnly = true)
    @Cacheable(value = "products", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #pageable.sort")
    public Page<ProductDTO> getAllProducts(Pageable pageable) {
        Page<Product> products = productRepository.findAll(pageable);
        return products.map(this::convertToDTO);
    }

    // 根据ID获取商品
    @Transactional(readOnly = true)
    @Cacheable(value = "product", key = "#id")
    public Optional<ProductDTO> getProductById(Long id) {
        return productRepository.findById(id).map(this::convertToDTO);
    }

    // 搜索商品
    @Transactional(readOnly = true)
    public Page<ProductDTO> searchProducts(String keyword, String category, String brand,
                                          BigDecimal minPrice, BigDecimal maxPrice, BigDecimal minRating,
                                          Pageable pageable) {
        Page<Product> products = productRepository.searchProducts(
            keyword, category, brand, minPrice, maxPrice, minRating, pageable);
        return products.map(this::convertToDTO);
    }

    // 获取热门商品
    @Transactional(readOnly = true)
    @Cacheable(value = "popularProducts", key = "#limit")
    public List<ProductDTO> getPopularProducts(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Product> products = productRepository.findPopularProducts(pageable);
        return products.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    // 获取推荐商品
    @Transactional(readOnly = true)
    @Cacheable(value = "recommendedProducts", key = "#productId + '_' + #limit")
    public List<ProductDTO> getRecommendedProducts(Long productId, int limit) {
        Optional<Product> product = productRepository.findById(productId);
        if (product.isPresent()) {
            String category = product.get().getCategory();
            Pageable pageable = PageRequest.of(0, limit);
            List<Product> products = productRepository.findRecommendedProducts(category, productId, pageable);
            return products.stream().map(this::convertToDTO).collect(Collectors.toList());
        }
        return List.of();
    }

    // 获取最新商品
    @Transactional(readOnly = true)
    @Cacheable(value = "latestProducts", key = "#limit")
    public List<ProductDTO> getLatestProducts(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Product> products = productRepository.findLatestProducts(pageable);
        return products.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    // 根据分类获取商品
    @Transactional(readOnly = true)
    public Page<ProductDTO> getProductsByCategory(String category, Pageable pageable) {
        Page<Product> products = productRepository.findByCategory(category, pageable);
        return products.map(this::convertToDTO);
    }

    // 根据品牌获取商品
    @Transactional(readOnly = true)
    public Page<ProductDTO> getProductsByBrand(String brand, Pageable pageable) {
        Page<Product> products = productRepository.findByBrand(brand, pageable);
        return products.map(this::convertToDTO);
    }

    // 创建商品
    public ProductDTO createProduct(ProductDTO productDTO) {
        Product product = convertToEntity(productDTO);
        Product savedProduct = productRepository.save(product);
        return convertToDTO(savedProduct);
    }

    // 更新商品
    public Optional<ProductDTO> updateProduct(Long id, ProductDTO productDTO) {
        return productRepository.findById(id).map(existingProduct -> {
            BeanUtils.copyProperties(productDTO, existingProduct, "id", "createdAt");
            Product updatedProduct = productRepository.save(existingProduct);
            return convertToDTO(updatedProduct);
        });
    }

    // 删除商品
    public boolean deleteProduct(Long id) {
        if (productRepository.existsById(id)) {
            productRepository.deleteById(id);
            return true;
        }
        return false;
    }

    // 获取所有分类
    @Transactional(readOnly = true)
    @Cacheable(value = "categories")
    public List<String> getAllCategories() {
        return productRepository.findAllCategories();
    }

    // 获取所有品牌
    @Transactional(readOnly = true)
    @Cacheable(value = "brands")
    public List<String> getAllBrands() {
        return productRepository.findAllBrands();
    }

    // 实体转DTO
    private ProductDTO convertToDTO(Product product) {
        ProductDTO dto = new ProductDTO();
        BeanUtils.copyProperties(product, dto);
        return dto;
    }

    // DTO转实体
    private Product convertToEntity(ProductDTO dto) {
        Product product = new Product();
        BeanUtils.copyProperties(dto, product, "id");
        return product;
    }

    // 创建排序对象
    public static Sort createSort(String sortBy, String sortOrder) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        return Sort.by(direction, sortBy != null ? sortBy : "createdAt");
    }
}
