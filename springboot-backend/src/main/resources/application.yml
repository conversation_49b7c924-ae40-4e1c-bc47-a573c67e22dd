server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: product-api
  
  # 数据库配置 - 开发环境使用H2，生产环境使用MySQL
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000

# 日志配置
logging:
  level:
    com.example.productapi: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# 跨域配置
cors:
  allowed-origins: "http://localhost:3000"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: true

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: *********************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: product_user
    password: your_password
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
