/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/config/DataInitializer.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/service/ProductService.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/controller/ProductController.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/repository/ProductRepository.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/ProductApiApplication.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/config/CorsConfig.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/entity/Category.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/entity/Product.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/dto/ProductDTO.java
/Users/<USER>/STEP/study/leetcode/front_workspace/aug_pop/springboot-backend/src/main/java/com/example/productapi/repository/CategoryRepository.java
