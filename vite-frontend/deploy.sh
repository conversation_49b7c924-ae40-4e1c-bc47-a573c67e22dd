#!/bin/bash

# 前端部署脚本
# 用于构建和部署前端应用到CDN

echo "🚀 开始构建前端应用..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 运行类型检查
echo "🔍 运行类型检查..."
npm run lint

# 构建生产版本
echo "🏗️ 构建生产版本..."
npm run build

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "📁 构建文件位于 dist/ 目录"
    
    # 显示构建文件大小
    echo "📊 构建文件大小："
    du -sh dist/*
    
    echo ""
    echo "🌐 部署说明："
    echo "1. 将 dist/ 目录下的所有文件上传到 CDN"
    echo "2. 配置 CDN 的回源地址为后端 API 服务器"
    echo "3. 设置适当的缓存策略"
    echo ""
    echo "📋 推荐的 CDN 配置："
    echo "- HTML 文件: 不缓存或短时间缓存"
    echo "- JS/CSS 文件: 长时间缓存（文件名包含hash）"
    echo "- 图片文件: 长时间缓存"
    
else
    echo "❌ 构建失败！"
    exit 1
fi
