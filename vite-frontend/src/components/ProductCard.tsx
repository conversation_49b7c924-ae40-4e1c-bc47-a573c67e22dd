import React from 'react';
import { Star, ShoppingCart } from 'lucide-react';
import { Product } from '@/types';
import { formatPrice, calculateDiscount, generateStars } from '@/utils';
import { useCartStore } from '@/store/cartStore';
import Button from './ui/Button';
import Card from './ui/Card';

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onClick }) => {
  const { addItem, isInCart, getItemQuantity } = useCartStore();
  
  const discount = product.originalPrice 
    ? calculateDiscount(product.originalPrice, product.price)
    : 0;
  
  const stars = generateStars(product.rating);
  const inCart = isInCart(product.id);
  const quantity = getItemQuantity(product.id);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addItem(product);
  };

  const handleCardClick = () => {
    onClick?.(product);
  };

  return (
    <Card className="product-card" hover>
      <div onClick={handleCardClick}>
        {/* 商品图片 */}
        <div className="relative overflow-hidden rounded-t-lg">
          <img
            src={product.imageUrl}
            alt={product.name}
            className="w-full h-48 object-cover transition-transform duration-300 hover:scale-105"
            loading="lazy"
          />
          
          {/* 折扣标签 */}
          {discount > 0 && (
            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
              -{discount}%
            </div>
          )}
          
          {/* 库存状态 */}
          {product.stock === 0 && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <span className="text-white font-medium">缺货</span>
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <Card.Content className="p-4">
          {/* 商品名称 */}
          <h3 className="font-medium text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors">
            {product.name}
          </h3>
          
          {/* 品牌 */}
          <p className="text-sm text-gray-500 mb-2">{product.brand}</p>
          
          {/* 评分 */}
          <div className="flex items-center mb-3">
            <div className="rating-stars">
              {stars.map((filled, index) => (
                <Star
                  key={index}
                  className={`w-4 h-4 ${filled ? 'star-filled' : 'star-empty'}`}
                  fill="currentColor"
                />
              ))}
            </div>
            <span className="text-sm text-gray-500 ml-2">
              ({product.reviewCount})
            </span>
          </div>
          
          {/* 价格 */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="price-current">
                {formatPrice(product.price)}
              </span>
              {product.originalPrice && product.originalPrice > product.price && (
                <span className="price-original">
                  {formatPrice(product.originalPrice)}
                </span>
              )}
            </div>
          </div>
        </Card.Content>
      </div>

      {/* 操作按钮 */}
      <Card.Footer className="p-4 pt-0">
        <Button
          onClick={handleAddToCart}
          disabled={product.stock === 0}
          className="w-full"
          variant={inCart ? "secondary" : "primary"}
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          {product.stock === 0 
            ? '缺货' 
            : inCart 
              ? `已添加 (${quantity})` 
              : '加入购物车'
          }
        </Button>
      </Card.Footer>
    </Card>
  );
};

export default ProductCard;
