import React, { useState, useCallback } from 'react';
import { Search, X } from 'lucide-react';
import { debounce } from '@/utils';
import Input from './ui/Input';
import Button from './ui/Button';

interface SearchBarProps {
  onSearch: (keyword: string) => void;
  placeholder?: string;
  defaultValue?: string;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = "搜索商品...",
  defaultValue = "",
  className,
}) => {
  const [keyword, setKeyword] = useState(defaultValue);

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onSearch(value.trim());
    }, 300),
    [onSearch]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
    debouncedSearch(value);
  };

  const handleClear = () => {
    setKeyword('');
    onSearch('');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(keyword.trim());
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          type="text"
          value={keyword}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="input pl-10 pr-10 w-full"
        />
        
        {keyword && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded-full"
            >
              <X className="h-4 w-4 text-gray-400" />
            </Button>
          </div>
        )}
      </div>
    </form>
  );
};

export default SearchBar;
