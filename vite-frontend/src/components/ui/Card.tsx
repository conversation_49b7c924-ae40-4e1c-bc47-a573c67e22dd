import React from 'react';
import { cn } from '@/utils';
import { BaseComponentProps } from '@/types';

interface CardProps extends BaseComponentProps {
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({ 
  children, 
  className, 
  hover = false 
}) => {
  return (
    <div className={cn(
      'card',
      hover && 'hover:shadow-md transition-shadow duration-200',
      className
    )}>
      {children}
    </div>
  );
};

const CardHeader: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-header', className)}>
      {children}
    </div>
  );
};

const CardContent: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-content', className)}>
      {children}
    </div>
  );
};

const CardFooter: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-footer', className)}>
      {children}
    </div>
  );
};

Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;
