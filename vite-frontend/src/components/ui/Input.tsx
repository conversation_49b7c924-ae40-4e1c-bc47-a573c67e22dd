import React from 'react';
import { cn } from '@/utils';
import { InputProps } from '@/types';

interface ExtendedInputProps extends InputProps, React.InputHTMLAttributes<HTMLInputElement> {}

const Input: React.FC<ExtendedInputProps> = ({
  label,
  error,
  className,
  ...props
}) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label}
          {props.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        className={cn(
          'input',
          error && 'border-red-500 focus-visible:ring-red-500',
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default Input;
