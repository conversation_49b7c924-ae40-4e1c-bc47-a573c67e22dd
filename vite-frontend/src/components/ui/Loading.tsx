import React from 'react';
import { cn } from '@/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ size = 'md', className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  return (
    <div className={cn('loading-spinner', sizeClasses[size], className)} />
  );
};

// 页面级加载组件
export const PageLoading: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <Loading size="lg" />
        <p className="mt-4 text-gray-600">加载中...</p>
      </div>
    </div>
  );
};

// 骨架屏组件
export const Skeleton: React.FC<{ className?: string }> = ({ className }) => {
  return <div className={cn('skeleton', className)} />;
};

// 商品卡片骨架屏
export const ProductCardSkeleton: React.FC = () => {
  return (
    <div className="card">
      <Skeleton className="h-48 w-full rounded-t-lg" />
      <div className="p-4 space-y-3">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    </div>
  );
};

export default Loading;
