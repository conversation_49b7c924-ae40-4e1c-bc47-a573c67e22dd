import { useQuery } from '@tanstack/react-query';
import { categoryApi } from '@/services/api';
import { ProductSearchParams } from '@/types';

// 获取所有分类
export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: categoryApi.getCategories,
    staleTime: 30 * 60 * 1000, // 分类数据缓存30分钟
    gcTime: 60 * 60 * 1000, // 1小时
  });
};

// 获取分类详情
export const useCategory = (id: number) => {
  return useQuery({
    queryKey: ['category', id],
    queryFn: () => categoryApi.getCategory(id),
    enabled: !!id,
    staleTime: 30 * 60 * 1000,
  });
};

// 获取分类下的商品
export const useCategoryProducts = (categoryId: number, params?: ProductSearchParams) => {
  return useQuery({
    queryKey: ['categoryProducts', categoryId, params],
    queryFn: () => categoryApi.getCategoryProducts(categoryId, params),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000,
  });
};
