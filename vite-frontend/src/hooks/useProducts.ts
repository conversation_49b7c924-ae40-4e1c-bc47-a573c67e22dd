import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { productApi } from '@/services/api';
import { ProductSearchParams } from '@/types';

// 获取商品列表
export const useProducts = (params?: ProductSearchParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productApi.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  });
};

// 获取商品详情
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => productApi.getProduct(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// 搜索商品
export const useSearchProducts = (keyword: string, params?: ProductSearchParams) => {
  return useQuery({
    queryKey: ['searchProducts', keyword, params],
    queryFn: () => productApi.searchProducts(keyword, params),
    enabled: !!keyword,
    staleTime: 2 * 60 * 1000, // 搜索结果缓存时间较短
  });
};

// 无限滚动获取商品
export const useInfiniteProducts = (params?: ProductSearchParams) => {
  return useInfiniteQuery({
    queryKey: ['infiniteProducts', params],
    queryFn: ({ pageParam = 0 }) => 
      productApi.getProducts({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      if (lastPage.last) return undefined;
      return lastPage.number + 1;
    },
    initialPageParam: 0,
    staleTime: 5 * 60 * 1000,
  });
};

// 获取热门商品
export const usePopularProducts = (limit: number = 10) => {
  return useQuery({
    queryKey: ['popularProducts', limit],
    queryFn: () => productApi.getPopularProducts(limit),
    staleTime: 10 * 60 * 1000, // 热门商品缓存时间较长
  });
};

// 获取推荐商品
export const useRecommendedProducts = (productId?: number, limit: number = 10) => {
  return useQuery({
    queryKey: ['recommendedProducts', productId, limit],
    queryFn: () => productApi.getRecommendedProducts(productId, limit),
    enabled: !!productId,
    staleTime: 10 * 60 * 1000,
  });
};
