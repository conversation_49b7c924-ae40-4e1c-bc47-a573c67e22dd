@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* 按钮组件样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }

  .btn-outline {
    @apply border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 active:bg-gray-100;
  }

  .btn-ghost {
    @apply bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* 输入框样式 */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* 卡片样式 */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* 商品卡片特殊样式 */
  .product-card {
    @apply card transition-all duration-200 hover:shadow-md hover:-translate-y-1 cursor-pointer;
  }

  .product-card:hover {
    @apply shadow-lg;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* 骨架屏动画 */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* 响应式网格 */
  .product-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6;
  }

  /* 价格显示 */
  .price-current {
    @apply text-lg font-bold text-red-600;
  }

  .price-original {
    @apply text-sm text-gray-500 line-through;
  }

  /* 评分星星 */
  .rating-stars {
    @apply flex items-center space-x-1;
  }

  .star-filled {
    @apply text-yellow-400;
  }

  .star-empty {
    @apply text-gray-300;
  }

  /* 标签样式 */
  .tag {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .tag-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .tag-secondary {
    @apply bg-gray-100 text-gray-800;
  }

  .tag-success {
    @apply bg-green-100 text-green-800;
  }

  .tag-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .tag-danger {
    @apply bg-red-100 text-red-800;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
