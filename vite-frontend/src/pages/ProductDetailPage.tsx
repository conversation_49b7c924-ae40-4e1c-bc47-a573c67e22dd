import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, ShoppingCart, ArrowLeft, Plus, Minus } from 'lucide-react';
import { useProduct, useRecommendedProducts } from '@/hooks/useProducts';
import { useCartStore } from '@/store/cartStore';
import { formatPrice, calculateDiscount, generateStars } from '@/utils';
import { PageLoading } from '@/components/ui/Loading';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import ProductGrid from '@/components/ProductGrid';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const productId = parseInt(id || '0');
  
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);

  const { data: product, isLoading, error } = useProduct(productId);
  const { data: recommendedProducts } = useRecommendedProducts(productId, 8);
  const { addItem, isInCart, getItemQuantity } = useCartStore();

  if (isLoading) return <PageLoading />;
  
  if (error || !product) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">😕</div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">商品未找到</h2>
        <p className="text-gray-600 mb-6">抱歉，您查找的商品不存在或已下架</p>
        <Button onClick={() => navigate('/')}>返回首页</Button>
      </div>
    );
  }

  const discount = product.originalPrice 
    ? calculateDiscount(product.originalPrice, product.price)
    : 0;
  
  const stars = generateStars(product.rating);
  const inCart = isInCart(product.id);
  const cartQuantity = getItemQuantity(product.id);
  const images = product.images || [product.imageUrl];

  const handleAddToCart = () => {
    addItem(product, quantity);
    setQuantity(1);
  };

  const handleQuantityChange = (delta: number) => {
    const newQuantity = Math.max(1, Math.min(product.stock, quantity + delta));
    setQuantity(newQuantity);
  };

  const handleProductClick = (clickedProduct: any) => {
    navigate(`/products/${clickedProduct.id}`);
  };

  return (
    <div className="space-y-8">
      {/* 返回按钮 */}
      <Button
        variant="ghost"
        onClick={() => navigate(-1)}
        className="flex items-center"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        返回
      </Button>

      {/* 商品详情 */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* 商品图片 */}
        <div className="space-y-4">
          <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
            <img
              src={images[selectedImage]}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          </div>
          
          {images.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-primary-500' : 'border-gray-200'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {product.name}
            </h1>
            <p className="text-lg text-gray-600">{product.brand}</p>
          </div>

          {/* 评分 */}
          <div className="flex items-center space-x-4">
            <div className="rating-stars">
              {stars.map((filled, index) => (
                <Star
                  key={index}
                  className={`w-5 h-5 ${filled ? 'star-filled' : 'star-empty'}`}
                  fill="currentColor"
                />
              ))}
            </div>
            <span className="text-gray-600">
              {product.rating} ({product.reviewCount} 评价)
            </span>
          </div>

          {/* 价格 */}
          <div className="space-y-2">
            <div className="flex items-center space-x-4">
              <span className="text-3xl font-bold text-red-600">
                {formatPrice(product.price)}
              </span>
              {product.originalPrice && product.originalPrice > product.price && (
                <>
                  <span className="text-xl text-gray-500 line-through">
                    {formatPrice(product.originalPrice)}
                  </span>
                  <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                    省 {formatPrice(product.originalPrice - product.price)}
                  </span>
                </>
              )}
            </div>
            {discount > 0 && (
              <p className="text-green-600 font-medium">
                限时优惠 {discount}% OFF
              </p>
            )}
          </div>

          {/* 库存状态 */}
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">库存:</span>
            <span className={`font-medium ${
              product.stock > 10 ? 'text-green-600' : 
              product.stock > 0 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {product.stock > 0 ? `${product.stock} 件` : '缺货'}
            </span>
          </div>

          {/* 数量选择 */}
          {product.stock > 0 && (
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">数量:</span>
              <div className="flex items-center border border-gray-300 rounded-lg">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(-1)}
                  disabled={quantity <= 1}
                  className="px-3 py-2"
                >
                  <Minus className="w-4 h-4" />
                </Button>
                <span className="px-4 py-2 min-w-[3rem] text-center">
                  {quantity}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(1)}
                  disabled={quantity >= product.stock}
                  className="px-3 py-2"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <Button
              onClick={handleAddToCart}
              disabled={product.stock === 0}
              className="flex-1"
              size="lg"
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              {product.stock === 0 ? '缺货' : '加入购物车'}
            </Button>
            {inCart && (
              <div className="flex items-center text-sm text-gray-600">
                购物车中已有 {cartQuantity} 件
              </div>
            )}
          </div>

          {/* 商品描述 */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-semibold">商品描述</h3>
            </Card.Header>
            <Card.Content>
              <p className="text-gray-700 leading-relaxed">
                {product.description}
              </p>
            </Card.Content>
          </Card>

          {/* 规格参数 */}
          {product.specifications && (
            <Card>
              <Card.Header>
                <h3 className="text-lg font-semibold">规格参数</h3>
              </Card.Header>
              <Card.Content>
                <div className="space-y-2">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600">{key}</span>
                      <span className="font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              </Card.Content>
            </Card>
          )}
        </div>
      </div>

      {/* 推荐商品 */}
      {recommendedProducts && recommendedProducts.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">相关推荐</h2>
          <ProductGrid
            products={recommendedProducts}
            onProductClick={handleProductClick}
          />
        </section>
      )}
    </div>
  );
};

export default ProductDetailPage;
