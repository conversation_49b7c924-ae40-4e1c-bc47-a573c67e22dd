import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Cart, CartItem, Product } from '@/types';

interface CartStore extends Cart {
  // 操作方法
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (productId: number) => number;
  isInCart: (productId: number) => boolean;
}

// 计算购物车总计
const calculateTotals = (items: CartItem[]) => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  return { totalItems, totalPrice };
};

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      totalItems: 0,
      totalPrice: 0,

      addItem: (product: Product, quantity: number = 1) => {
        set((state) => {
          const existingItem = state.items.find(item => item.id === product.id);
          
          let newItems: CartItem[];
          if (existingItem) {
            // 如果商品已存在，更新数量
            newItems = state.items.map(item =>
              item.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            );
          } else {
            // 如果商品不存在，添加新项目
            const newItem: CartItem = {
              id: product.id,
              product,
              quantity,
              selectedAt: new Date().toISOString(),
            };
            newItems = [...state.items, newItem];
          }

          const { totalItems, totalPrice } = calculateTotals(newItems);
          return {
            items: newItems,
            totalItems,
            totalPrice,
          };
        });
      },

      removeItem: (productId: number) => {
        set((state) => {
          const newItems = state.items.filter(item => item.id !== productId);
          const { totalItems, totalPrice } = calculateTotals(newItems);
          return {
            items: newItems,
            totalItems,
            totalPrice,
          };
        });
      },

      updateQuantity: (productId: number, quantity: number) => {
        if (quantity <= 0) {
          get().removeItem(productId);
          return;
        }

        set((state) => {
          const newItems = state.items.map(item =>
            item.id === productId
              ? { ...item, quantity }
              : item
          );
          const { totalItems, totalPrice } = calculateTotals(newItems);
          return {
            items: newItems,
            totalItems,
            totalPrice,
          };
        });
      },

      clearCart: () => {
        set({
          items: [],
          totalItems: 0,
          totalPrice: 0,
        });
      },

      getItemQuantity: (productId: number) => {
        const item = get().items.find(item => item.id === productId);
        return item ? item.quantity : 0;
      },

      isInCart: (productId: number) => {
        return get().items.some(item => item.id === productId);
      },
    }),
    {
      name: 'cart-storage', // localStorage key
      partialize: (state) => ({
        items: state.items,
        totalItems: state.totalItems,
        totalPrice: state.totalPrice,
      }),
    }
  )
);
